package service

import (
	"audit-service/internal/models"
	"audit-service/internal/repository"
	"encoding/json"
	"time"
)

type AuditService interface {
	CreateAuditLog(req *models.CreateAuditLogRequest) error
}

type auditService struct {
	auditRepo repository.AuditRepository
}

func NewAuditService(auditRepo repository.AuditRepository) AuditService {
	return &auditService{
		auditRepo: auditRepo,
	}
}

func (s *auditService) CreateAuditLog(req *models.CreateAuditLogRequest) error {
	// Convert details to JSON
	var detailsJSON json.RawMessage
	if req.Details != nil {
		detailsBytes, err := json.Marshal(req.Details)
		if err != nil {
			return err
		}
		detailsJSON = detailsBytes
	}

	auditLog := &models.AuditLog{
		EventType:    req.EventType,
		UserID:       req.UserID,
		ResourceType: req.ResourceType,
		ResourceID:   req.ResourceID,
		Action:       req.Action,
		Details:      detailsJSON,
		IPAddress:    req.IPAddress,
		UserAgent:    req.UserAgent,
		Timestamp:    time.Now(),
	}

	return s.auditRepo.CreateAuditLog(auditLog)
}
