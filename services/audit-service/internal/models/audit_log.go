package models

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

type AuditLog struct {
	ID           uint            `json:"id" gorm:"primaryKey"`
	EventType    string          `json:"event_type" gorm:"not null;size:100"`
	UserID       *uint           `json:"user_id,omitempty" gorm:"index"`
	ResourceType *string         `json:"resource_type,omitempty" gorm:"size:100"`
	ResourceID   *string         `json:"resource_id,omitempty" gorm:"size:100"`
	Action       string          `json:"action" gorm:"not null;size:50"`
	Details      json.RawMessage `json:"details,omitempty" gorm:"type:jsonb"`
	IPAddress    *string         `json:"ip_address,omitempty" gorm:"type:inet"`
	UserAgent    *string         `json:"user_agent,omitempty" gorm:"type:text"`
	Timestamp    time.Time       `json:"timestamp" gorm:"default:CURRENT_TIMESTAMP"`
	CreatedAt    time.Time       `json:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at"`
	DeletedAt    gorm.DeletedAt  `json:"-" gorm:"index"`
}

// CreateAuditLogRequest represents the request to create an audit log from Kafka events
type CreateAuditLogRequest struct {
	EventType    string         `json:"event_type"`
	UserID       *uint          `json:"user_id,omitempty"`
	ResourceType *string        `json:"resource_type,omitempty"`
	ResourceID   *string        `json:"resource_id,omitempty"`
	Action       string         `json:"action"`
	Details      map[string]any `json:"details,omitempty"`
	IPAddress    *string        `json:"ip_address,omitempty"`
	UserAgent    *string        `json:"user_agent,omitempty"`
}

// KafkaEvent represents an event received from Kafka
type KafkaEvent struct {
	EventType string         `json:"event_type"`
	UserID    any            `json:"user_id"`
	Email     string         `json:"email,omitempty"`
	Timestamp time.Time      `json:"timestamp"`
	Details   map[string]any `json:",inline"`
}
