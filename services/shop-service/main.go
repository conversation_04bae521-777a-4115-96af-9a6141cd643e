package main

import (
	"log"
	"os"

	"shop-service/internal/config"
	"shop-service/internal/database"
	"shop-service/internal/handlers"
	"shop-service/internal/kafka"
	"shop-service/internal/middleware"
	"shop-service/internal/repository"
	"shop-service/internal/service"

	"github.com/gin-gonic/gin"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize database
	db, err := database.Connect(cfg.MongoURI)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Initialize Kafka producer
	kafkaProducer, err := kafka.NewProducer(cfg.KafkaBrokers)
	if err != nil {
		log.Fatal("Failed to initialize Kafka producer:", err)
	}
	defer kafkaProducer.Close()

	// Initialize repositories
	productRepo := repository.NewProductRepository(db)
	cartRepo := repository.NewCartRepository(db)
	orderRepo := repository.NewOrderRepository(db)

	// Initialize services
	productService := service.NewProductService(productRepo, kafkaProducer)
	cartService := service.NewCartService(cartRepo, productRepo, kafkaProducer)
	orderService := service.NewOrderService(orderRepo, cartRepo, kafkaProducer)

	// Initialize handlers
	productHandler := handlers.NewProductHandler(productService)
	cartHandler := handlers.NewCartHandler(cartService)
	orderHandler := handlers.NewOrderHandler(orderService)

	// Setup router
	router := gin.Default()

	// Add middleware
	router.Use(middleware.CORS())
	router.Use(middleware.Logger())

	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "healthy"})
	})

	// Shop routes
	v1 := router.Group("/v1")
	{
		// Product routes
		v1.GET("/products", productHandler.GetProducts)
		v1.GET("/products/:id", productHandler.GetProduct)
		v1.POST("/products", middleware.AuthRequired(cfg.AuthServiceURL), productHandler.CreateProduct)
		v1.PUT("/products/:id", middleware.AuthRequired(cfg.AuthServiceURL), productHandler.UpdateProduct)
		v1.DELETE("/products/:id", middleware.AuthRequired(cfg.AuthServiceURL), productHandler.DeleteProduct)

		// Cart routes
		v1.GET("/cart", middleware.AuthRequired(cfg.AuthServiceURL), cartHandler.GetCart)
		v1.POST("/cart/add", middleware.AuthRequired(cfg.AuthServiceURL), cartHandler.AddToCart)
		v1.PUT("/cart/items/:id", middleware.AuthRequired(cfg.AuthServiceURL), cartHandler.UpdateCartItem)
		v1.DELETE("/cart/items/:id", middleware.AuthRequired(cfg.AuthServiceURL), cartHandler.RemoveFromCart)
		v1.DELETE("/cart", middleware.AuthRequired(cfg.AuthServiceURL), cartHandler.ClearCart)

		// Order routes
		v1.POST("/checkout", middleware.AuthRequired(cfg.AuthServiceURL), orderHandler.Checkout)
		v1.GET("/orders", middleware.AuthRequired(cfg.AuthServiceURL), orderHandler.GetOrders)
		v1.GET("/orders/:id", middleware.AuthRequired(cfg.AuthServiceURL), orderHandler.GetOrder)
	}

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("Shop service starting on port %s", port)
	if err := router.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
