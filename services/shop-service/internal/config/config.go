package config

import (
	"os"
	"strings"
)

type Config struct {
	MongoURI       string
	AuthServiceURL string
	KafkaBrokers   []string
	Port           string
}

func Load() *Config {
	mongoHost := getEnv("MONGO_HOST", "localhost")
	mongoPort := getEnv("MONGO_PORT", "27017")
	mongoUser := getEnv("MONGO_USER", "ponyo")
	mongoPassword := getEnv("MONGO_PASSWORD", "ponyo123")
	mongoDatabase := getEnv("MONGO_DATABASE", "shop_db")

	var mongoURI string
	if mongoUser != "" && mongoPassword != "" {
		mongoURI = "mongodb://" + mongoUser + ":" + mongoPassword + "@" + mongoHost + ":" + mongoPort + "/" + mongoDatabase
	} else {
		mongoURI = "mongodb://" + mongoHost + ":" + mongoPort + "/" + mongoDatabase
	}

	kafkaBrokers := strings.Split(getEnv("KAFKA_BROKERS", "localhost:9092"), ",")

	return &Config{
		MongoURI:       mongoURI,
		AuthServiceURL: getEnv("AUTH_SERVICE_URL", "http://localhost:8001"),
		KafkaBrokers:   kafkaBrokers,
		Port:           getEnv("PORT", "8080"),
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
