package database

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Database struct {
	Client   *mongo.Client
	Database *mongo.Database
}

func Connect(mongoURI string) (*Database, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Set client options
	clientOptions := options.Client().ApplyURI(mongoURI)

	// Connect to MongoDB
	client, err := mongo.Connect(clientOptions)
	if err != nil {
		return nil, err
	}

	// Ping the database to verify connection
	err = client.Ping(ctx, nil)
	if err != nil {
		return nil, err
	}

	// Get database name from URI or use default
	dbName := "shop_db"
	if clientOptions.Auth != nil && clientOptions.Auth.AuthSource != "" {
		dbName = clientOptions.Auth.AuthSource
	}

	database := client.Database(dbName)

	return &Database{
		Client:   client,
		Database: database,
	}, nil
}

func (db *Database) Close() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	return db.Client.Disconnect(ctx)
}

func (db *Database) GetCollection(name string) *mongo.Collection {
	return db.Database.Collection(name)
}
