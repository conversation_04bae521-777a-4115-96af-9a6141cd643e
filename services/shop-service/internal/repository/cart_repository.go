package repository

import (
	"context"
	"shop-service/internal/database"
	"shop-service/internal/models"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
)

type CartRepository interface {
	GetByUserID(userID uint) (*models.Cart, error)
	Create(cart *models.Cart) error
	Update(cart *models.Cart) error
	Delete(userID uint) error
	AddItem(userID uint, item models.CartItem) error
	UpdateItem(userID uint, productID string, quantity int) error
	RemoveItem(userID uint, productID string) error
}

type cartRepository struct {
	collection *mongo.Collection
}

func NewCartRepository(db *database.Database) CartRepository {
	return &cartRepository{
		collection: db.GetCollection("carts"),
	}
}

func (r *cartRepository) GetByUserID(userID uint) (*models.Cart, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var cart models.Cart
	err := r.collection.FindOne(ctx, bson.M{"user_id": userID}).Decode(&cart)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			// Create new cart if not exists
			cart = models.Cart{
				UserID:    userID,
				Items:     []models.CartItem{},
				Total:     0,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			}
			_, err = r.collection.InsertOne(ctx, cart)
			if err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}

	return &cart, nil
}

func (r *cartRepository) Create(cart *models.Cart) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	cart.CreatedAt = time.Now()
	cart.UpdatedAt = time.Now()

	_, err := r.collection.InsertOne(ctx, cart)
	return err
}

func (r *cartRepository) Update(cart *models.Cart) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	cart.UpdatedAt = time.Now()
	filter := bson.M{"user_id": cart.UserID}
	update := bson.M{"$set": cart}

	_, err := r.collection.UpdateOne(ctx, filter, update)
	return err
}

func (r *cartRepository) Delete(userID uint) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := r.collection.DeleteOne(ctx, bson.M{"user_id": userID})
	return err
}

func (r *cartRepository) AddItem(userID uint, item models.CartItem) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Check if item already exists in cart
	filter := bson.M{
		"user_id":           userID,
		"items.product_id": item.ProductID,
	}

	var existingCart models.Cart
	err := r.collection.FindOne(ctx, filter).Decode(&existingCart)
	if err == nil {
		// Item exists, update quantity
		update := bson.M{
			"$inc": bson.M{"items.$.quantity": item.Quantity},
			"$set": bson.M{"updated_at": time.Now()},
		}
		_, err = r.collection.UpdateOne(ctx, filter, update)
	} else if err == mongo.ErrNoDocuments {
		// Item doesn't exist, add new item
		filter = bson.M{"user_id": userID}
		update := bson.M{
			"$push": bson.M{"items": item},
			"$set":  bson.M{"updated_at": time.Now()},
		}
		_, err = r.collection.UpdateOne(ctx, filter, update)
	}

	return err
}

func (r *cartRepository) UpdateItem(userID uint, productID string, quantity int) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	objectID, err := bson.ObjectIDFromHex(productID)
	if err != nil {
		return err
	}

	filter := bson.M{
		"user_id":           userID,
		"items.product_id": objectID,
	}

	update := bson.M{
		"$set": bson.M{
			"items.$.quantity":  quantity,
			"updated_at":       time.Now(),
		},
	}

	_, err = r.collection.UpdateOne(ctx, filter, update)
	return err
}

func (r *cartRepository) RemoveItem(userID uint, productID string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	objectID, err := bson.ObjectIDFromHex(productID)
	if err != nil {
		return err
	}

	filter := bson.M{"user_id": userID}
	update := bson.M{
		"$pull": bson.M{"items": bson.M{"product_id": objectID}},
		"$set":  bson.M{"updated_at": time.Now()},
	}

	_, err = r.collection.UpdateOne(ctx, filter, update)
	return err
}
