package kafka

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/segmentio/kafka-go"
)

type Producer struct {
	writer *kafka.Writer
}

func NewProducer(brokers []string) (*Producer, error) {
	writer := &kafka.Writer{
		Addr:         kafka.TCP(brokers...),
		Balancer:     &kafka.LeastBytes{},
		BatchTimeout: 10 * time.Millisecond,
		BatchSize:    100,
	}

	return &Producer{writer: writer}, nil
}

func (p *Producer) PublishEvent(topic string, event interface{}) error {
	eventBytes, err := json.Marshal(event)
	if err != nil {
		return err
	}

	message := kafka.Message{
		Topic: topic,
		Value: eventBytes,
		Time:  time.Now(),
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err = p.writer.WriteMessages(ctx, message)
	if err != nil {
		log.Printf("Failed to publish event to topic %s: %v", topic, err)
		return err
	}

	log.Printf("Event published to topic %s: %s", topic, string(eventBytes))
	return nil
}

func (p *Producer) Close() error {
	return p.writer.Close()
}
