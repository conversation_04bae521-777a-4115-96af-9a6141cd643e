package service

import (
	"errors"
	"shop-service/internal/kafka"
	"shop-service/internal/models"
	"shop-service/internal/repository"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
)

type OrderService interface {
	Checkout(userID uint, req *models.CheckoutRequest) (*models.Order, error)
	GetOrder(id string, userID uint) (*models.Order, error)
	GetOrders(userID uint, page, limit int) ([]models.Order, int64, error)
	UpdateOrderStatus(id string, status models.OrderStatus) error
}

type orderService struct {
	orderRepo     repository.OrderRepository
	cartRepo      repository.CartRepository
	kafkaProducer *kafka.Producer
}

func NewOrderService(orderRepo repository.OrderRepository, cartRepo repository.CartRepository, kafkaProducer *kafka.Producer) OrderService {
	return &orderService{
		orderRepo:     orderRepo,
		cartRepo:      cartRepo,
		kafkaProducer: kafkaProducer,
	}
}

func (s *orderService) Checkout(userID uint, req *models.CheckoutRequest) (*models.Order, error) {
	// Get user's cart
	cart, err := s.cartRepo.GetByUserID(userID)
	if err != nil {
		return nil, err
	}

	if len(cart.Items) == 0 {
		return nil, errors.New("cart is empty")
	}

	// Convert cart items to order items
	var orderItems []models.OrderItem
	total := 0.0

	for _, cartItem := range cart.Items {
		orderItem := models.OrderItem{
			ProductID:   cartItem.ProductID,
			ProductName: cartItem.Product.Name,
			Quantity:    cartItem.Quantity,
			Price:       cartItem.Price,
			Total:       cartItem.Total,
		}
		orderItems = append(orderItems, orderItem)
		total += cartItem.Total
	}

	// Create order
	order := &models.Order{
		ID:              bson.NewObjectID(),
		UserID:          userID,
		Items:           orderItems,
		Total:           total,
		Status:          models.OrderStatusPending,
		ShippingAddress: req.ShippingAddress,
		PaymentMethod:   req.PaymentMethod,
	}

	if err := s.orderRepo.Create(order); err != nil {
		return nil, err
	}

	// Clear cart after successful order creation
	s.cartRepo.Delete(userID)

	// Publish event to Kafka
	s.kafkaProducer.PublishEvent("shop.events", map[string]interface{}{
		"event_type":      "order_created",
		"order_id":        order.ID.Hex(),
		"user_id":         userID,
		"total":           total,
		"items_count":     len(orderItems),
		"payment_method":  req.PaymentMethod,
		"timestamp":       time.Now(),
	})

	return order, nil
}

func (s *orderService) GetOrder(id string, userID uint) (*models.Order, error) {
	order, err := s.orderRepo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// Verify order belongs to user
	if order.UserID != userID {
		return nil, errors.New("order not found")
	}

	return order, nil
}

func (s *orderService) GetOrders(userID uint, page, limit int) ([]models.Order, int64, error) {
	if page <= 0 {
		page = 1
	}
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	return s.orderRepo.GetByUserID(userID, page, limit)
}

func (s *orderService) UpdateOrderStatus(id string, status models.OrderStatus) error {
	// Validate status
	validStatuses := map[models.OrderStatus]bool{
		models.OrderStatusPending:   true,
		models.OrderStatusConfirmed: true,
		models.OrderStatusShipped:   true,
		models.OrderStatusDelivered: true,
		models.OrderStatusCancelled: true,
	}

	if !validStatuses[status] {
		return errors.New("invalid order status")
	}

	// Get existing order
	order, err := s.orderRepo.GetByID(id)
	if err != nil {
		return err
	}

	// Update status
	if err := s.orderRepo.UpdateStatus(id, status); err != nil {
		return err
	}

	// Publish event to Kafka
	s.kafkaProducer.PublishEvent("shop.events", map[string]interface{}{
		"event_type":    "order_status_updated",
		"order_id":      id,
		"user_id":       order.UserID,
		"old_status":    order.Status,
		"new_status":    status,
		"timestamp":     time.Now(),
	})

	return nil
}
