package service

import (
	"errors"
	"shop-service/internal/kafka"
	"shop-service/internal/models"
	"shop-service/internal/repository"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
)

type ProductService interface {
	CreateProduct(req *models.CreateProductRequest) (*models.Product, error)
	GetProduct(id string) (*models.Product, error)
	GetProducts(page, limit int) ([]models.Product, int64, error)
	UpdateProduct(id string, req *models.UpdateProductRequest) (*models.Product, error)
	DeleteProduct(id string) error
	GetProductsByCategory(category string, page, limit int) ([]models.Product, int64, error)
	SearchProducts(query string, page, limit int) ([]models.Product, int64, error)
}

type productService struct {
	productRepo   repository.ProductRepository
	kafkaProducer *kafka.Producer
}

func NewProductService(productRepo repository.ProductRepository, kafkaProducer *kafka.Producer) ProductService {
	return &productService{
		productRepo:   productRepo,
		kafkaProducer: kafkaProducer,
	}
}

func (s *productService) CreateProduct(req *models.CreateProductRequest) (*models.Product, error) {
	product := &models.Product{
		ID:          bson.NewObjectID(),
		Name:        req.Name,
		Description: req.Description,
		Price:       req.Price,
		Category:    req.Category,
		Stock:       req.Stock,
		ImageURL:    req.ImageURL,
		IsActive:    true,
	}

	if err := s.productRepo.Create(product); err != nil {
		return nil, err
	}

	// Publish event to Kafka
	s.kafkaProducer.PublishEvent("shop.events", map[string]interface{}{
		"event_type":  "product_created",
		"product_id":  product.ID.Hex(),
		"name":        product.Name,
		"category":    product.Category,
		"price":       product.Price,
		"stock":       product.Stock,
		"timestamp":   time.Now(),
	})

	return product, nil
}

func (s *productService) GetProduct(id string) (*models.Product, error) {
	return s.productRepo.GetByID(id)
}

func (s *productService) GetProducts(page, limit int) ([]models.Product, int64, error) {
	if page <= 0 {
		page = 1
	}
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	return s.productRepo.GetAll(page, limit)
}

func (s *productService) UpdateProduct(id string, req *models.UpdateProductRequest) (*models.Product, error) {
	// Get existing product
	existingProduct, err := s.productRepo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// Update fields if provided
	if req.Name != nil {
		existingProduct.Name = *req.Name
	}
	if req.Description != nil {
		existingProduct.Description = *req.Description
	}
	if req.Price != nil {
		if *req.Price <= 0 {
			return nil, errors.New("price must be greater than 0")
		}
		existingProduct.Price = *req.Price
	}
	if req.Category != nil {
		existingProduct.Category = *req.Category
	}
	if req.Stock != nil {
		if *req.Stock < 0 {
			return nil, errors.New("stock cannot be negative")
		}
		existingProduct.Stock = *req.Stock
	}
	if req.ImageURL != nil {
		existingProduct.ImageURL = *req.ImageURL
	}
	if req.IsActive != nil {
		existingProduct.IsActive = *req.IsActive
	}

	if err := s.productRepo.Update(id, existingProduct); err != nil {
		return nil, err
	}

	// Publish event to Kafka
	s.kafkaProducer.PublishEvent("shop.events", map[string]interface{}{
		"event_type":  "product_updated",
		"product_id":  existingProduct.ID.Hex(),
		"name":        existingProduct.Name,
		"category":    existingProduct.Category,
		"price":       existingProduct.Price,
		"stock":       existingProduct.Stock,
		"is_active":   existingProduct.IsActive,
		"timestamp":   time.Now(),
	})

	return existingProduct, nil
}

func (s *productService) DeleteProduct(id string) error {
	// Check if product exists
	_, err := s.productRepo.GetByID(id)
	if err != nil {
		return err
	}

	if err := s.productRepo.Delete(id); err != nil {
		return err
	}

	// Publish event to Kafka
	s.kafkaProducer.PublishEvent("shop.events", map[string]interface{}{
		"event_type":  "product_deleted",
		"product_id":  id,
		"timestamp":   time.Now(),
	})

	return nil
}

func (s *productService) GetProductsByCategory(category string, page, limit int) ([]models.Product, int64, error) {
	if page <= 0 {
		page = 1
	}
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	return s.productRepo.GetByCategory(category, page, limit)
}

func (s *productService) SearchProducts(query string, page, limit int) ([]models.Product, int64, error) {
	if page <= 0 {
		page = 1
	}
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	return s.productRepo.Search(query, page, limit)
}
