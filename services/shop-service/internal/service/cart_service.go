package service

import (
	"errors"
	"shop-service/internal/kafka"
	"shop-service/internal/models"
	"shop-service/internal/repository"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
)

type CartService interface {
	GetCart(userID uint) (*models.Cart, error)
	AddToCart(userID uint, req *models.AddToCartRequest) (*models.Cart, error)
	UpdateCartItem(userID uint, productID string, req *models.UpdateCartItemRequest) (*models.Cart, error)
	RemoveFromCart(userID uint, productID string) (*models.Cart, error)
	ClearCart(userID uint) error
}

type cartService struct {
	cartRepo      repository.CartRepository
	productRepo   repository.ProductRepository
	kafkaProducer *kafka.Producer
}

func NewCartService(cartRepo repository.CartRepository, productRepo repository.ProductRepository, kafkaProducer *kafka.Producer) CartService {
	return &cartService{
		cartRepo:      cartRepo,
		productRepo:   productRepo,
		kafkaProducer: kafkaProducer,
	}
}

func (s *cartService) GetCart(userID uint) (*models.Cart, error) {
	cart, err := s.cartRepo.GetByUserID(userID)
	if err != nil {
		return nil, err
	}

	// Calculate total and populate product details
	total := 0.0
	for i := range cart.Items {
		item := &cart.Items[i]
		
		// Get product details
		product, err := s.productRepo.GetByID(item.ProductID.Hex())
		if err != nil {
			continue // Skip if product not found
		}
		
		item.Product = product
		item.Price = product.Price
		item.Total = item.Price * float64(item.Quantity)
		total += item.Total
	}

	cart.Total = total
	s.cartRepo.Update(cart)

	return cart, nil
}

func (s *cartService) AddToCart(userID uint, req *models.AddToCartRequest) (*models.Cart, error) {
	// Validate product exists and is available
	product, err := s.productRepo.GetByID(req.ProductID)
	if err != nil {
		return nil, errors.New("product not found")
	}

	if !product.IsActive {
		return nil, errors.New("product is not available")
	}

	if product.Stock < req.Quantity {
		return nil, errors.New("insufficient stock")
	}

	// Convert product ID to ObjectID
	productObjectID, err := bson.ObjectIDFromHex(req.ProductID)
	if err != nil {
		return nil, errors.New("invalid product ID")
	}

	// Create cart item
	cartItem := models.CartItem{
		ProductID: productObjectID,
		Quantity:  req.Quantity,
		Price:     product.Price,
		Total:     product.Price * float64(req.Quantity),
	}

	// Add item to cart
	if err := s.cartRepo.AddItem(userID, cartItem); err != nil {
		return nil, err
	}

	// Publish event to Kafka
	s.kafkaProducer.PublishEvent("shop.events", map[string]interface{}{
		"event_type":  "item_added_to_cart",
		"user_id":     userID,
		"product_id":  req.ProductID,
		"quantity":    req.Quantity,
		"timestamp":   time.Now(),
	})

	// Return updated cart
	return s.GetCart(userID)
}

func (s *cartService) UpdateCartItem(userID uint, productID string, req *models.UpdateCartItemRequest) (*models.Cart, error) {
	// Validate product exists
	product, err := s.productRepo.GetByID(productID)
	if err != nil {
		return nil, errors.New("product not found")
	}

	if product.Stock < req.Quantity {
		return nil, errors.New("insufficient stock")
	}

	// Update cart item
	if err := s.cartRepo.UpdateItem(userID, productID, req.Quantity); err != nil {
		return nil, err
	}

	// Publish event to Kafka
	s.kafkaProducer.PublishEvent("shop.events", map[string]interface{}{
		"event_type":  "cart_item_updated",
		"user_id":     userID,
		"product_id":  productID,
		"quantity":    req.Quantity,
		"timestamp":   time.Now(),
	})

	// Return updated cart
	return s.GetCart(userID)
}

func (s *cartService) RemoveFromCart(userID uint, productID string) (*models.Cart, error) {
	// Remove item from cart
	if err := s.cartRepo.RemoveItem(userID, productID); err != nil {
		return nil, err
	}

	// Publish event to Kafka
	s.kafkaProducer.PublishEvent("shop.events", map[string]interface{}{
		"event_type":  "item_removed_from_cart",
		"user_id":     userID,
		"product_id":  productID,
		"timestamp":   time.Now(),
	})

	// Return updated cart
	return s.GetCart(userID)
}

func (s *cartService) ClearCart(userID uint) error {
	// Delete cart
	if err := s.cartRepo.Delete(userID); err != nil {
		return err
	}

	// Publish event to Kafka
	s.kafkaProducer.PublishEvent("shop.events", map[string]interface{}{
		"event_type":  "cart_cleared",
		"user_id":     userID,
		"timestamp":   time.Now(),
	})

	return nil
}
