package config

import (
	"fmt"
	"os"
	"strings"
)

type Config struct {
	DatabaseURL    string
	AuthServiceURL string
	KafkaBrokers   []string
	Port           string
}

func Load() *Config {
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "4004")
	dbUser := getEnv("DB_USER", "ponyo")
	dbPassword := getEnv("DB_PASSWORD", "ponyo123")
	dbName := getEnv("DB_NAME", "user_db")

	databaseURL := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	kafkaBrokers := strings.Split(getEnv("KAFKA_BROKERS", "localhost:9092"), ",")

	return &Config{
		DatabaseURL:    databaseURL,
		AuthServiceURL: getEnv("AUTH_SERVICE_URL", "http://localhost:8001"),
		KafkaBrokers:   kafkaBrokers,
		Port:           getEnv("PORT", "8080"),
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
