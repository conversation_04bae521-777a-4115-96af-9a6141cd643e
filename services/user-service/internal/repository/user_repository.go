package repository

import (
	"user-service/internal/models"

	"gorm.io/gorm"
)

type UserRepository interface {
	// Profile operations
	GetProfile(userID uint) (*models.UserProfile, error)
	CreateProfile(profile *models.UserProfile) error
	UpdateProfile(profile *models.UserProfile) error

	// Address operations
	GetAddresses(userID uint) ([]models.UserAddress, error)
	GetAddressByID(id uint, userID uint) (*models.UserAddress, error)
	CreateAddress(address *models.UserAddress) error
	UpdateAddress(address *models.UserAddress) error
	DeleteAddress(id uint, userID uint) error

	// Preferences operations
	GetPreferences(userID uint) (*models.UserPreferences, error)
	CreatePreferences(preferences *models.UserPreferences) error
	UpdatePreferences(preferences *models.UserPreferences) error
}

type userRepository struct {
	db *gorm.DB
}

func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepository{db: db}
}

// Profile operations
func (r *userRepository) GetProfile(userID uint) (*models.UserProfile, error) {
	var profile models.UserProfile
	err := r.db.Where("user_id = ?", userID).First(&profile).Error
	if err != nil {
		return nil, err
	}
	return &profile, nil
}

func (r *userRepository) CreateProfile(profile *models.UserProfile) error {
	return r.db.Create(profile).Error
}

func (r *userRepository) UpdateProfile(profile *models.UserProfile) error {
	return r.db.Save(profile).Error
}

// Address operations
func (r *userRepository) GetAddresses(userID uint) ([]models.UserAddress, error) {
	var addresses []models.UserAddress
	err := r.db.Where("user_id = ?", userID).Find(&addresses).Error
	return addresses, err
}

func (r *userRepository) GetAddressByID(id uint, userID uint) (*models.UserAddress, error) {
	var address models.UserAddress
	err := r.db.Where("id = ? AND user_id = ?", id, userID).First(&address).Error
	if err != nil {
		return nil, err
	}
	return &address, nil
}

func (r *userRepository) CreateAddress(address *models.UserAddress) error {
	return r.db.Create(address).Error
}

func (r *userRepository) UpdateAddress(address *models.UserAddress) error {
	return r.db.Save(address).Error
}

func (r *userRepository) DeleteAddress(id uint, userID uint) error {
	return r.db.Where("id = ? AND user_id = ?", id, userID).Delete(&models.UserAddress{}).Error
}

// Preferences operations
func (r *userRepository) GetPreferences(userID uint) (*models.UserPreferences, error) {
	var preferences models.UserPreferences
	err := r.db.Where("user_id = ?", userID).First(&preferences).Error
	if err != nil {
		return nil, err
	}
	return &preferences, nil
}

func (r *userRepository) CreatePreferences(preferences *models.UserPreferences) error {
	return r.db.Create(preferences).Error
}

func (r *userRepository) UpdatePreferences(preferences *models.UserPreferences) error {
	return r.db.Save(preferences).Error
}
