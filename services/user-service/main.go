package main

import (
	"log"
	"os"

	"user-service/internal/config"
	"user-service/internal/database"
	"user-service/internal/handlers"
	"user-service/internal/kafka"
	"user-service/internal/middleware"
	"user-service/internal/repository"
	"user-service/internal/service"

	"github.com/gin-gonic/gin"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize database
	db, err := database.Connect(cfg.DatabaseURL)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Initialize Kafka producer
	kafkaProducer, err := kafka.NewProducer(cfg.KafkaBrokers)
	if err != nil {
		log.Fatal("Failed to initialize Kafka producer:", err)
	}
	defer kafkaProducer.Close()

	// Initialize repository
	userRepo := repository.NewUserRepository(db)

	// Initialize service
	userService := service.NewUserService(userRepo, kafkaProducer, cfg.AuthServiceURL)

	// Initialize handlers
	userHandler := handlers.NewUserHandler(userService)

	// Setup router
	router := gin.Default()

	// Add middleware
	router.Use(middleware.CORS())
	router.Use(middleware.Logger())

	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "healthy"})
	})

	// User routes
	v1 := router.Group("/v1")
	{
		v1.GET("/profile", middleware.AuthRequired(cfg.AuthServiceURL), userHandler.GetProfile)
		v1.PUT("/profile", middleware.AuthRequired(cfg.AuthServiceURL), userHandler.UpdateProfile)
		v1.GET("/addresses", middleware.AuthRequired(cfg.AuthServiceURL), userHandler.GetAddresses)
		v1.POST("/addresses", middleware.AuthRequired(cfg.AuthServiceURL), userHandler.CreateAddress)
		v1.PUT("/addresses/:id", middleware.AuthRequired(cfg.AuthServiceURL), userHandler.UpdateAddress)
		v1.DELETE("/addresses/:id", middleware.AuthRequired(cfg.AuthServiceURL), userHandler.DeleteAddress)
		v1.GET("/preferences", middleware.AuthRequired(cfg.AuthServiceURL), userHandler.GetPreferences)
		v1.PUT("/preferences", middleware.AuthRequired(cfg.AuthServiceURL), userHandler.UpdatePreferences)
	}

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("User service starting on port %s", port)
	if err := router.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
