import { useState, useEffect } from 'react'
import api from '../services/api'

const Cart = () => {
  const [cart, setCart] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchCart()
  }, [])

  const fetchCart = async () => {
    try {
      setLoading(true)
      const response = await api.get('/shop/v1/cart')
      setCart(response.data)
    } catch (err) {
      setError('Failed to load cart')
      console.error('Error fetching cart:', err)
    } finally {
      setLoading(false)
    }
  }

  const updateQuantity = async (itemId, quantity) => {
    try {
      await api.put(`/shop/v1/cart/items/${itemId}`, { quantity })
      fetchCart() // Refresh cart
    } catch (err) {
      alert('Failed to update quantity')
      console.error('Error updating quantity:', err)
    }
  }

  const removeItem = async (itemId) => {
    try {
      await api.delete(`/shop/v1/cart/items/${itemId}`)
      fetchCart() // Refresh cart
    } catch (err) {
      alert('Failed to remove item')
      console.error('Error removing item:', err)
    }
  }

  const checkout = async () => {
    try {
      const response = await api.post('/shop/v1/checkout')
      alert('Order placed successfully!')
      setCart({ items: [], total: 0 }) // Clear cart
    } catch (err) {
      alert('Checkout failed')
      console.error('Error during checkout:', err)
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    )
  }

  if (!cart || !cart.items || cart.items.length === 0) {
    return (
      <div className="text-center py-12">
        <h1 className="text-3xl font-bold mb-4">Your Cart</h1>
        <p className="text-gray-600 text-lg">Your cart is empty</p>
        <a 
          href="/products" 
          className="inline-block mt-4 bg-primary-600 text-white px-6 py-3 rounded hover:bg-primary-700 transition-colors"
        >
          Continue Shopping
        </a>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">Your Cart</h1>
      
      <div className="bg-white rounded-lg shadow-md">
        {cart.items.map((item) => (
          <div key={item.id} className="flex items-center p-6 border-b border-gray-200 last:border-b-0">
            <img
              src={item.product.image_url || '/placeholder-product.jpg'}
              alt={item.product.name}
              className="w-20 h-20 object-cover rounded"
            />
            <div className="flex-1 ml-4">
              <h3 className="text-lg font-semibold">{item.product.name}</h3>
              <p className="text-gray-600">${item.product.price}</p>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => updateQuantity(item.id, item.quantity - 1)}
                disabled={item.quantity <= 1}
                className="bg-gray-200 text-gray-700 w-8 h-8 rounded disabled:opacity-50"
              >
                -
              </button>
              <span className="text-lg font-medium">{item.quantity}</span>
              <button
                onClick={() => updateQuantity(item.id, item.quantity + 1)}
                className="bg-gray-200 text-gray-700 w-8 h-8 rounded"
              >
                +
              </button>
              <button
                onClick={() => removeItem(item.id)}
                className="ml-4 text-red-600 hover:text-red-800"
              >
                Remove
              </button>
            </div>
            <div className="ml-6 text-lg font-semibold">
              ${(item.product.price * item.quantity).toFixed(2)}
            </div>
          </div>
        ))}
        
        <div className="p-6 bg-gray-50">
          <div className="flex justify-between items-center mb-4">
            <span className="text-xl font-semibold">Total:</span>
            <span className="text-2xl font-bold text-primary-600">
              ${cart.total?.toFixed(2) || '0.00'}
            </span>
          </div>
          <button
            onClick={checkout}
            className="w-full bg-primary-600 text-white py-3 px-6 rounded-lg hover:bg-primary-700 transition-colors text-lg font-semibold"
          >
            Proceed to Checkout
          </button>
        </div>
      </div>
    </div>
  )
}

export default Cart
