import { Link } from 'react-router-dom'

const Home = () => {
  return (
    <div className="text-center">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-5xl font-bold text-gray-900 mb-6">
          Welcome to WebShop
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          A modern microservices-based e-commerce platform built with React, Go, and Docker
        </p>
        
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-2xl font-semibold mb-4">🛍️ Shop Products</h3>
            <p className="text-gray-600 mb-4">
              Browse our extensive catalog of products with advanced filtering and search capabilities.
            </p>
            <Link 
              to="/products"
              className="inline-block bg-primary-600 text-white px-6 py-3 rounded hover:bg-primary-700 transition-colors"
            >
              Browse Products
            </Link>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-2xl font-semibold mb-4">👤 User Account</h3>
            <p className="text-gray-600 mb-4">
              Create an account to manage your profile, addresses, and order history.
            </p>
            <Link 
              to="/register"
              className="inline-block bg-green-600 text-white px-6 py-3 rounded hover:bg-green-700 transition-colors"
            >
              Get Started
            </Link>
          </div>
        </div>

        <div className="bg-gray-100 p-8 rounded-lg">
          <h2 className="text-3xl font-bold mb-6">Architecture Features</h2>
          <div className="grid md:grid-cols-3 gap-6 text-left">
            <div>
              <h4 className="font-semibold text-lg mb-2">🏗️ Microservices</h4>
              <p className="text-gray-600">
                Scalable architecture with separate services for authentication, user management, and shopping.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-lg mb-2">📊 Observability</h4>
              <p className="text-gray-600">
                Complete logging and monitoring with Grafana, Loki, and structured logging.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-lg mb-2">🚀 Performance</h4>
              <p className="text-gray-600">
                Optimized with NGINX load balancing, database optimization, and caching strategies.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Home
