# WebShop - Microservices E-commerce Platform

A comprehensive microservices-based e-commerce web application built with modern technologies focusing on scalability, performance, and observability.

## 🏗️ Architecture Overview

This project implements a microservices architecture with the following components:

### Frontend
- **React.js** with Vite for fast development
- **Tailwind CSS** for modern, responsive styling
- **Zustand** for state management
- **React Query** for server state management
- **React Router** for client-side routing

### Backend Services
- **Auth Service** - User authentication and JWT management
- **User Service** - User profiles, addresses, and preferences
- **Shop Service** - Product catalog, cart, and orders
- **Audit Service** - Event logging and audit trails

### Infrastructure
- **NGINX** - API Gateway, load balancing, and static file serving
- **PostgreSQL** - Relational data for users, auth, and audit logs
- **MongoDB** - Document storage for products and orders
- **Apache Kafka** - Event streaming and inter-service communication
- **Grafana + Loki** - Logging and monitoring stack
- **Docker & Docker Compose** - Containerization and orchestration

## 🚀 Quick Start

### Prerequisites
- <PERSON><PERSON> and <PERSON>er Compose
- Node.js 18+ (for frontend development)
- Go 1.21+ (for backend development)

### Running the Application

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ponyo
   ```

2. **Start all services**
   ```bash
   docker-compose up -d
   ```

3. **Build and start frontend (for development)**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - API Gateway: http://localhost:80
   - Grafana Dashboard: http://localhost:3000 (admin/admin123)

### Service Endpoints

| Service | Port | Health Check |
|---------|------|--------------|
| Auth Service | 8001 | http://localhost:8001/health |
| User Service | 8002 | http://localhost:8002/health |
| Shop Service | 8003 | http://localhost:8003/health |
| Audit Service | 8004 | http://localhost:8004/health |
| NGINX Gateway | 80 | http://localhost/health |

## 📊 API Documentation

### Authentication Endpoints
```
POST /api/auth/v1/register - User registration
POST /api/auth/v1/login - User login
POST /api/auth/v1/refresh - Refresh access token
POST /api/auth/v1/logout - User logout
GET  /api/auth/v1/verify - Verify token
```

### User Management Endpoints
```
GET  /api/users/v1/profile - Get user profile
PUT  /api/users/v1/profile - Update user profile
GET  /api/users/v1/addresses - Get user addresses
POST /api/users/v1/addresses - Create new address
PUT  /api/users/v1/addresses/:id - Update address
DELETE /api/users/v1/addresses/:id - Delete address
```

### Shop Endpoints
```
GET  /api/shop/v1/products - List products
GET  /api/shop/v1/products/:id - Get product details
POST /api/shop/v1/cart/add - Add item to cart
GET  /api/shop/v1/cart - Get cart contents
POST /api/shop/v1/checkout - Process checkout
```

## 🔧 Development

### Backend Development

Each service is a standalone Go application with the following structure:
```
services/[service-name]/
├── main.go
├── go.mod
├── Dockerfile
└── internal/
    ├── config/
    ├── database/
    ├── handlers/
    ├── middleware/
    ├── models/
    ├── repository/
    └── service/
```

### Frontend Development

The React frontend uses modern development practices:
```
frontend/
├── src/
│   ├── components/
│   ├── pages/
│   ├── services/
│   ├── stores/
│   └── utils/
├── package.json
└── vite.config.js
```

### Running Individual Services

1. **Start dependencies**
   ```bash
   docker-compose up postgres mongodb kafka zookeeper -d
   ```

2. **Run a service locally**
   ```bash
   cd services/auth-service
   go mod tidy
   go run main.go
   ```

## 📈 Monitoring and Logging

### Grafana Dashboard
- URL: http://localhost:3000
- Username: admin
- Password: admin123

### Log Aggregation
- Logs are collected by fluent-bit
- Stored in Loki
- Visualized in Grafana

### Structured Logging
All services use structured JSON logging with fields:
- `timestamp`
- `level`
- `service`
- `message`
- `trace_id` (for request tracing)

## 🔒 Security Features

- JWT-based authentication
- Password hashing with bcrypt
- CORS protection
- Rate limiting via NGINX
- Input validation
- SQL injection prevention with GORM
- XSS protection headers

## 🗄️ Database Schema

### PostgreSQL (Auth Service)
- `users` - User credentials and basic info
- `refresh_tokens` - JWT refresh tokens

### PostgreSQL (User Service)
- `user_profiles` - Extended user information
- `user_addresses` - Shipping/billing addresses
- `user_preferences` - User settings

### PostgreSQL (Audit Service)
- `audit_logs` - Immutable audit trail

### MongoDB (Shop Service)
- `products` - Product catalog
- `carts` - Shopping carts
- `orders` - Order history

## 🚀 Deployment

### Production Deployment
1. Update environment variables in docker-compose.yml
2. Configure SSL certificates in nginx/ssl/
3. Set up proper secrets management
4. Configure backup strategies for databases
5. Set up monitoring alerts

### Scaling
- Services can be horizontally scaled using Docker Swarm or Kubernetes
- Database read replicas can be added
- NGINX can be configured for load balancing multiple service instances

## 🧪 Testing

### Running Tests
```bash
# Backend tests
cd services/auth-service
go test ./...

# Frontend tests
cd frontend
npm test
```

### API Testing
Use the provided Postman collection or curl commands:
```bash
# Register user
curl -X POST http://localhost/api/auth/v1/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🔗 Additional Resources

- [Go Documentation](https://golang.org/doc/)
- [React Documentation](https://reactjs.org/docs/)
- [Docker Documentation](https://docs.docker.com/)
- [Kafka Documentation](https://kafka.apache.org/documentation/)
- [Grafana Documentation](https://grafana.com/docs/)
