#!/bin/bash

# API testing script for WebShop microservices

BASE_URL="http://localhost"
AUTH_TOKEN=""

echo "🧪 Testing WebShop API endpoints..."

# Test health endpoints
echo "🔍 Testing health endpoints..."
for service in auth users shop audit; do
    port=""
    case $service in
        auth) port="8001" ;;
        users) port="8002" ;;
        shop) port="8003" ;;
        audit) port="8004" ;;
    esac
    
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:$port/health)
    if [ "$response" = "200" ]; then
        echo "✅ $service service health check passed"
    else
        echo "❌ $service service health check failed (HTTP $response)"
    fi
done

# Test API Gateway health
response=$(curl -s -o /dev/null -w "%{http_code}" $BASE_URL/health)
if [ "$response" = "200" ]; then
    echo "✅ API Gateway health check passed"
else
    echo "❌ API Gateway health check failed (HTTP $response)"
fi

echo ""
echo "👤 Testing authentication..."

# Test user registration
echo "📝 Testing user registration..."
REGISTER_RESPONSE=$(curl -s -X POST $BASE_URL/api/auth/v1/register \
    -H "Content-Type: application/json" \
    -d '{
        "email": "<EMAIL>",
        "password": "password123"
    }')

if echo "$REGISTER_RESPONSE" | grep -q "access_token"; then
    echo "✅ User registration successful"
    AUTH_TOKEN=$(echo "$REGISTER_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    echo "🔑 Auth token obtained"
else
    echo "❌ User registration failed"
    echo "Response: $REGISTER_RESPONSE"
fi

# Test token verification
if [ ! -z "$AUTH_TOKEN" ]; then
    echo "🔐 Testing token verification..."
    VERIFY_RESPONSE=$(curl -s -X GET $BASE_URL/api/auth/v1/verify \
        -H "Authorization: Bearer $AUTH_TOKEN")
    
    if echo "$VERIFY_RESPONSE" | grep -q "valid"; then
        echo "✅ Token verification successful"
    else
        echo "❌ Token verification failed"
        echo "Response: $VERIFY_RESPONSE"
    fi
fi

echo ""
echo "🛍️ Testing shop endpoints..."

# Test products endpoint
echo "📦 Testing products endpoint..."
PRODUCTS_RESPONSE=$(curl -s -X GET $BASE_URL/api/shop/v1/products)
if [ "$?" = "0" ]; then
    echo "✅ Products endpoint accessible"
else
    echo "❌ Products endpoint failed"
fi

# Test protected cart endpoint
if [ ! -z "$AUTH_TOKEN" ]; then
    echo "🛒 Testing cart endpoint..."
    CART_RESPONSE=$(curl -s -X GET $BASE_URL/api/shop/v1/cart \
        -H "Authorization: Bearer $AUTH_TOKEN")
    
    if [ "$?" = "0" ]; then
        echo "✅ Cart endpoint accessible"
    else
        echo "❌ Cart endpoint failed"
        echo "Response: $CART_RESPONSE"
    fi
fi

echo ""
echo "📊 Testing user service..."

# Test user profile endpoint
if [ ! -z "$AUTH_TOKEN" ]; then
    echo "👤 Testing user profile endpoint..."
    PROFILE_RESPONSE=$(curl -s -X GET $BASE_URL/api/users/v1/profile \
        -H "Authorization: Bearer $AUTH_TOKEN")
    
    if [ "$?" = "0" ]; then
        echo "✅ User profile endpoint accessible"
    else
        echo "❌ User profile endpoint failed"
        echo "Response: $PROFILE_RESPONSE"
    fi
fi

echo ""
echo "🎉 API testing complete!"
echo ""
echo "💡 Tips:"
echo "   - Check docker-compose logs if any tests failed"
echo "   - Ensure all services are running: docker-compose ps"
echo "   - View service logs: docker-compose logs [service-name]"
