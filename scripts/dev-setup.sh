#!/bin/bash

# Development setup script for WebShop microservices

set -e

echo "🚀 Setting up WebShop development environment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

echo "✅ Docker is running"

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p infrastructure/nginx/ssl
mkdir -p logs

# Generate self-signed SSL certificates for development
echo "🔐 Generating SSL certificates for development..."
if [ ! -f infrastructure/nginx/ssl/cert.pem ]; then
    openssl req -x509 -newkey rsa:4096 -keyout infrastructure/nginx/ssl/key.pem -out infrastructure/nginx/ssl/cert.pem -days 365 -nodes -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
    echo "✅ SSL certificates generated"
else
    echo "✅ SSL certificates already exist"
fi

# Start infrastructure services first
echo "🏗️ Starting infrastructure services..."
docker-compose up -d postgres mongodb kafka zookeeper loki grafana

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 30

# Check if services are healthy
echo "🔍 Checking service health..."
for service in postgres mongodb kafka; do
    if docker-compose ps $service | grep -q "Up"; then
        echo "✅ $service is running"
    else
        echo "❌ $service failed to start"
        docker-compose logs $service
        exit 1
    fi
done

# Start application services
echo "🚀 Starting application services..."
docker-compose up -d auth-service user-service shop-service audit-service

# Wait for application services
sleep 20

# Start NGINX
echo "🌐 Starting NGINX API Gateway..."
docker-compose up -d nginx

# Setup frontend dependencies
if [ -d "frontend" ]; then
    echo "📦 Installing frontend dependencies..."
    cd frontend
    if command -v npm &> /dev/null; then
        npm install
        echo "✅ Frontend dependencies installed"
    else
        echo "⚠️ npm not found. Please install Node.js and npm to set up the frontend."
    fi
    cd ..
fi

echo ""
echo "🎉 Development environment setup complete!"
echo ""
echo "📋 Service URLs:"
echo "   Frontend (dev):     http://localhost:3000"
echo "   API Gateway:        http://localhost:80"
echo "   Auth Service:       http://localhost:8001"
echo "   User Service:       http://localhost:8002"
echo "   Shop Service:       http://localhost:8003"
echo "   Audit Service:      http://localhost:8004"
echo "   Grafana:           http://localhost:3000 (admin/admin123)"
echo ""
echo "🔧 Development commands:"
echo "   Start frontend:     cd frontend && npm run dev"
echo "   View logs:          docker-compose logs -f [service-name]"
echo "   Stop all:           docker-compose down"
echo "   Restart service:    docker-compose restart [service-name]"
echo ""
echo "📚 Check README.md for more information!"
