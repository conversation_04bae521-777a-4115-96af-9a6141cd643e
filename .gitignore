# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Go specific
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work

# Go vendor directory
vendor/

# Go build cache
.cache/

# Docker
.dockerignore

# Database files
*.db
*.sqlite
*.sqlite3

# SSL certificates (development only)
infrastructure/nginx/ssl/*.pem
infrastructure/nginx/ssl/*.key
infrastructure/nginx/ssl/*.crt

# Log files
logs/
*.log

# Build artifacts
build/
dist/
target/

# IDE files
.vscode/
.idea/
*.iml

# Test coverage
coverage/
*.cover
*.coverprofile

# Backup files
*.bak
*.backup
*.old

# Local development files
.local/
local/

# Docker volumes data
postgres_data/
mongodb_data/
grafana_data/

# Kafka data
kafka_data/
zookeeper_data/

# Frontend build
frontend/dist/
frontend/build/

# Package lock files (keep yarn.lock or package-lock.json, not both)
# package-lock.json
# yarn.lock

# Environment specific files
.env.production
.env.staging
.env.development

# Secrets
secrets/
*.secret
*.key
*.pem
*.p12
*.pfx

# Monitoring and logging
prometheus_data/
loki_data/
grafana_data/

# Temporary test files
test_output/
test_results/
