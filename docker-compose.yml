services:
  # Databases
  postgres:
    image: postgres:17.4-alpine3.20
    container_name: ponyo-postgres
    environment:
      POSTGRES_DB: ponyo
      POSTGRES_USER: ponyo
      POSTGRES_PASSWORD: ponyo123
    ports:
      - "4004:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/databases/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - ponyo-net

  mongodb:
    image: bitnami/mongodb:8.0-debian-12
    container_name: ponyo-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: ponyo
      MONGO_INITDB_ROOT_PASSWORD: ponyo123
      MONGO_INITDB_DATABASE: shop_db
    ports:
      - "4005:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - ponyo-net

  # # Message Queue
  # zookeeper:
  #   image: confluentinc/cp-zookeeper:7.4.0
  #   environment:
  #     ZOOKEEPER_CLIENT_PORT: 2181
  #     ZOOKEEPER_TICK_TIME: 2000
  #   networks:
  #     - ponyo-net

  kafka:
    image: apache/kafka-native:4.0.0
    container_name: ponyo-kafka
    # depends_on:
    #   - zookeeper
    ports:
      - "4002:9092"
    environment:
      - KAFKA_NODE_ID=1
      - KAFKA_PROCESS_ROLES=broker,controller
      - KAFKA_LISTENERS=PLAINTEXT://0.0.0.0:9092,CONTROLLER://0.0.0.0:9093
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092
      - KAFKA_CONTROLLER_LISTENER_NAMES=CONTROLLER
      - KAFKA_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT
      - KAFKA_CONTROLLER_QUORUM_VOTERS=1@kafka:9093
      - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
      - KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR=1
      - KAFKA_TRANSACTION_STATE_LOG_MIN_ISR=1
      - KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS=0
      - KAFKA_NUM_PARTITIONS=3
      # KAFKA_BROKER_ID: 1
      # KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      # KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      # KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      # KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    networks:
      - ponyo-net

  kafkaui:
    container_name: ponyo-kafkaui
    image: provectuslabs/kafka-ui:v0.7.2
    environment:
      - DYNAMIC_CONFIG_ENABLED=true
      - SERVER_SERVLET_CONTEXT_PATH=/kafkaui
    ports:
      - "4003:8080"
    networks:
      - ponyo-net

  # Microservices
  auth-service:
    build: ./services/auth-service
    container_name: ponyo-auth-service
    ports:
      - "8001:8080"
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: ponyo
      DB_PASSWORD: ponyo123
      DB_NAME: auth_db
      JWT_SECRET: your-super-secret-jwt-key
      KAFKA_BROKERS: kafka:9092
    depends_on:
      - postgres
      - kafka
    networks:
      - ponyo-net

  user-service:
    build: ./services/user-service
    container_name: ponyo-user-service
    ports:
      - "8002:8080"
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: ponyo
      DB_PASSWORD: ponyo123
      DB_NAME: user_db
      AUTH_SERVICE_URL: http://auth-service:8080
      KAFKA_BROKERS: kafka:9092
    depends_on:
      - postgres
      - kafka
      - auth-service
    networks:
      - ponyo-net

  shop-service:
    build: ./services/shop-service
    container_name: ponyo-shop-service
    ports:
      - "8003:8080"
    environment:
      MONGO_URI: *************************************************************
      AUTH_SERVICE_URL: http://auth-service:8080
      KAFKA_BROKERS: kafka:9092
    depends_on:
      - mongodb
      - kafka
      - auth-service
    networks:
      - ponyo-net

  audit-service:
    build: ./services/audit-service
    container_name: ponyo-audit-service
    ports:
      - "8004:8080"
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: ponyo
      DB_PASSWORD: ponyo123
      DB_NAME: audit_db
      KAFKA_BROKERS: kafka:9092
    depends_on:
      - postgres
      - kafka
    networks:
      - ponyo-net

  # API Gateway
  nginx:
    image: nginx:1.26-alpine3.20
    container_name: ponyo-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infrastructure/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./infrastructure/nginx/ssl:/etc/nginx/ssl
      - ./frontend/dist:/usr/share/nginx/html
    depends_on:
      - auth-service
      - user-service
      - shop-service
      - audit-service
    networks:
      - ponyo-net

  # Logging Stack
  loki:
    image: grafana/loki:3.4.1
    container_name: ponyo-loki
    ports:
      - "4006:3100"
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - ./infrastructure/logging/loki-config.yaml:/etc/loki/local-config.yaml
    networks:
      - ponyo-net

  grafana:
    image: grafana/grafana:11.2.8
    container_name: ponyo-grafana
    ports:
      - "4007:3000"
    environment:
      # - GF_PATHS_PROVISIONING=/etc/grafana/provisioning
      - GF_FEATURE_TOGGLES_ENABLE=alertingSimplifiedRouting,alertingQueryAndExpressionsStepMode
      - GF_SERVER_ROOT_URL=http://localhost/grafana
      - GF_SECURITY_ADMIN_PASSWORD= admin123
    entrypoint:
      - sh
      - -euc
      - |
        mkdir -p /etc/grafana/provisioning/datasources
        cat <<EOF > /etc/grafana/provisioning/datasources/ds.yaml
        apiVersion: 1
        datasources:
        - name: Loki
          type: loki
          access: proxy 
          orgId: 1
          url: http://loki:3100
          basicAuth: false
          isDefault: true
          version: 1
          editable: false
        EOF
        /run.sh
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/logging/grafana/provisioning:/etc/grafana/provisioning
    networks:
      - ponyo-net

  fluent-bit:
    image: fluent/fluent-bit:3.1.9-amd64
    container_name: ponyo-fluentbit
    ports:
      - "4008:24224/udp"  # UDP port for log ingestion
    volumes:
      - ./infrastructure/logging/fluent-bit.conf:/fluent-bit/etc/fluent-bit.conf
      - /var/log:/var/log:ro
    depends_on:
      - loki
    networks:
      - ponyo-net

volumes:
  postgres_data:
  mongodb_data:
  grafana_data:

networks:
  ponyo-net:
    driver: bridge
